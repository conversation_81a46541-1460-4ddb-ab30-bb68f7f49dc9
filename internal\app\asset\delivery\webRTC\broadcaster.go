package webrtc

import (
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"sync"

	"github.com/pion/interceptor"
	"github.com/pion/interceptor/pkg/intervalpli"
	"github.com/pion/webrtc/v4"
)

type Broadcaster struct {
	CameraID   string
	LocalTrack *webrtc.TrackLocalStaticRTP
	SourcePC   *webrtc.PeerConnection
	Viewers    map[string][]*webrtc.PeerConnection
	TrackLock  sync.Mutex
	ViewerLock sync.Mutex
}

func NewBroadcaster(cameraID string) *Broadcaster {
	return &Broadcaster{
		CameraID: cameraID,
		Viewers:  make(map[string][]*webrtc.PeerConnection),
	}
}

func (b *Broadcaster) AddViewer(viewerID string, offer webrtc.SessionDescription) (*webrtc.SessionDescription, error) {
	//1. Check if the source track is ready
	b.TrackLock.Lock()
	if b.LocalTrack == nil {
		b.TrackLock.Unlock()
		return nil, errors.New("stream not ready")
	}
	localTrack := b.LocalTrack
	b.TrackLock.Unlock()

	// 2. Add the viewer's PeerConnection to the list of viewers
	peerConnection, error := webrtc.NewPeerConnection(webrtc.Configuration{
		ICEServers: []webrtc.ICEServer{
			{
				URLs: []string{"stun:stun.l.google.com:19302"},
			},
		},
	})
	if error != nil {
		return nil, error
	}
	// 3. Add the LocalTrack (from the camera source) to the Viewer's PeerConnection
	// This is the core "broadcasting" part for viewers: sending the captured stream.
	rtpSender, err := peerConnection.AddTrack(localTrack)
	if err != nil {
		return nil, err
	}
	// 4. Handle RTCP (Receiver Report) from Viewer
	go func() {
		rtcpBuf := make([]byte, 1500)
		for {
			// Continuously read RTCP packets from the RTPSender.
			// This is important for receiving Receiver Reports (RR) and Picture Loss Indication (PLI)
			// from the viewer, which help with congestion control and error recovery.
			if _, _, rtcpErr := rtpSender.Read(rtcpBuf); rtcpErr != nil {
				if errors.Is(err, io.EOF) {
					fmt.Println("Viewer disconnected")
					return
				} else {
					fmt.Printf("Error reading RTCP: %v\n", rtcpErr)
				}
				b.ViewerLock.Lock()
				delete(b.Viewers, viewerID)
				b.ViewerLock.Unlock()
				_ = peerConnection.Close()
				return
			}
		}
	}()
	// 5. SDP Exchange (Signaling with the Viewer)
	if err := peerConnection.SetRemoteDescription(offer); err != nil {
		return nil, err
	}
	answer, err := peerConnection.CreateAnswer(nil)
	if err != nil {
		return nil, err
	}
	gatherComplete := webrtc.GatheringCompletePromise(peerConnection)
	if err = peerConnection.SetLocalDescription(answer); err != nil {
		return nil, err
	}
	<-gatherComplete
	// 6. Store the Viewer PeerConnection
	b.ViewerLock.Lock()
	b.Viewers[viewerID] = append(b.Viewers[viewerID], peerConnection)
	b.ViewerLock.Unlock()
	return peerConnection.LocalDescription(), nil

}

func (b *Broadcaster) StartBroadcast(offer webrtc.SessionDescription) (*webrtc.SessionDescription, error) {
	// 1. Media Engine Setup
	mediaEngine := &webrtc.MediaEngine{}
	if err := mediaEngine.RegisterDefaultCodecs(); err != nil {
		panic(err)
	}
	// 2. Interceptor Registry Setup (for PLI)
	// Create a InterceptorRegistry. This is the user configurable RTP/RTCP Pipeline.
	// This provides NACKs, RTCP Reports and other features. If you use `webrtc.NewPeerConnection`
	// this is enabled by default. If you are manually managing You MUST create a InterceptorRegistry
	// for each PeerConnection.
	interceptorRegistry := &interceptor.Registry{}

	// Use the default set of Interceptors
	if err := webrtc.RegisterDefaultInterceptors(mediaEngine, interceptorRegistry); err != nil {
		panic(err)
	}

	// Register a intervalpli factory
	// This interceptor sends a PLI every 3 seconds. A PLI causes a video keyframe to be generated by the sender.
	// This makes our video seekable and more error resilent, but at a cost of lower picture quality and higher bitrates
	// A real world application should process incoming RTCP packets from viewers and forward them to senders
	intervalPliFactory, err := intervalpli.NewReceiverInterceptor()
	if err != nil {
		panic(err)
	}
	interceptorRegistry.Add(intervalPliFactory)
	// 3. WebRTC API Creation and PeerConnection Creation for the Source (Publisher)
	// Create a new RTCPeerConnection
	peerConnection, err := webrtc.NewAPI(
		webrtc.WithMediaEngine(mediaEngine),
		webrtc.WithInterceptorRegistry(interceptorRegistry),
	).NewPeerConnection(webrtc.Configuration{
		ICEServers: []webrtc.ICEServer{
			{
				URLs: []string{"stun:stun.l.google.com:19302"},
			},
		},
	})
	if err != nil {
		panic(err)
	}
	defer func() {
		if cErr := peerConnection.Close(); cErr != nil {
			fmt.Printf("cannot close peerConnection: %v\n", cErr)
		}
	}()
	// 4. Add Transceiver for Video
	// Allow us to receive 1 video track
	if _, err = peerConnection.AddTransceiverFromKind(webrtc.RTPCodecTypeVideo); err != nil {
		panic(err)
	}
	// 5. OnTrack Callback: Where the Magic Happens for Receiving Stream
	// This callback is fired when the remote peer (the camera/publisher) adds a track.
	peerConnection.OnTrack(func(remoteTrack *webrtc.TrackRemote, receiver *webrtc.RTPReceiver) {
		// Create a local static RTP track. This is where the received video data will be buffered.
		localTrack, err := webrtc.NewTrackLocalStaticRTP(remoteTrack.Codec().RTPCodecCapability, "video", b.CameraID)
		if err != nil {
			panic(err) // Panics here indicate a serious unrecoverable error
		}
		// Store this local track in the Broadcaster struct for later use by viewers
		b.TrackLock.Lock()
		b.LocalTrack = localTrack
		b.TrackLock.Unlock()

		rtpBuf := make([]byte, 1400) // Buffer for reading RTP packets
		for {
			// Continuously read RTP packets from the remote track (from the camera)
			n, _, err := remoteTrack.Read(rtpBuf)
			if err != nil {
				// If the remote track ends (e.g., connection closes), log and exit goroutine
				if errors.Is(err, io.EOF) {
					fmt.Println("Remote track EOF. Publisher disconnected.")
					b.TrackLock.Lock()
					b.LocalTrack = nil // Clear the track as it's no longer valid
					b.TrackLock.Unlock()
					return
				}
				// Other errors (e.g., network issues)
				fmt.Printf("Error reading from remote track: %v\n", err)
				return
			}

			// Write the received RTP packets to the LocalTrack.
			// This is the core "broadcasting" part for the source: capturing the incoming stream.
			b.TrackLock.Lock()
			if b.LocalTrack != nil { // Ensure track still exists before writing
				_, err = b.LocalTrack.Write(rtpBuf[:n]) // Write the received data to the local buffer
			}
			b.TrackLock.Unlock()
		}
	})
	// 7. SDP Exchange (Signaling with the Source)
	// Set the remote offer (from the camera/publisher)
	if err = peerConnection.SetRemoteDescription(offer); err != nil {
		return nil, err
	}

	// Create an answer for the source's offer
	answer, err := peerConnection.CreateAnswer(nil)
	if err != nil {
		return nil, err
	}

	// Wait for ICE gathering to complete before returning the local description
	gatherComplete := webrtc.GatheringCompletePromise(peerConnection)
	if err = peerConnection.SetLocalDescription(answer); err != nil {
		return nil, err
	}
	<-gatherComplete // Block until ICE gathering is done

	// 8. Store the Source PeerConnection
	b.SourcePC = peerConnection
	return peerConnection.LocalDescription(), nil // Return the generated answer SDP
}
func DecodeSDP(b64 string) (webrtc.SessionDescription, error) {
	decoded, err := base64.StdEncoding.DecodeString(b64)
	if err != nil {
		return webrtc.SessionDescription{}, err
	}
	// Note: It hardcodes SDPTypeOffer. This might be problematic if you also need to decode answers.
	return webrtc.SessionDescription{SDP: string(decoded), Type: webrtc.SDPTypeOffer}, nil
}

func EncodeSDP(sdp *webrtc.SessionDescription) string {
	return base64.StdEncoding.EncodeToString([]byte(sdp.SDP))
}
