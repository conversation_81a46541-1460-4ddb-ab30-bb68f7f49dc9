package models

import (
	"time"

	"github.com/google/uuid"
)

type Snapshot struct {
	Base
	CameraID    uuid.UUID `json:"camera_id" gorm:"not null"`
	Camera      Camera    `gorm:"foreignKey:CameraID" json:"camera"`
	Description string    `json:"description" gorm:"type:text"`
	Timestamp   time.Time `json:"timestamp" gorm:"not null"`
	FileName    string    `json:"file_name" gorm:"not null"`
	FileSize    int64     `json:"file_size" gorm:"not null"`
	FileType    string    `json:"file_type" gorm:"not null"`
	FilePath    string    `json:"file_path" gorm:"not null"`
}
