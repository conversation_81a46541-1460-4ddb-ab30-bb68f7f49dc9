package services

import (
	"context"
	"encoding/base64"
	webrtc "smart-city/internal/app/camera/delivery/webRTC"
	"smart-city/internal/app/camera/dto"
	camRepositories "smart-city/internal/app/camera/repository"
	premiseRepositories "smart-city/internal/app/premise/repository"
	"smart-city/internal/models"

	"path/filepath"
	"smart-city/pkg/errors"
	"smart-city/pkg/utils"

	"github.com/google/uuid"
)

type Service struct {
	cameraRepo   camRepositories.CameraRepository
	premiseRepo  premiseRepositories.PremiseRepository
	snapshotRepo camRepositories.SnapshotRepository
}

func NewCameraService(cameraRepo camRepositories.CameraRepository, premiseRepo premiseRepositories.PremiseRepository, snapshotRepo camRepositories.SnapshotRepository) *Service {
	return &Service{cameraRepo: cameraRepo, premiseRepo: premiseRepo, snapshotRepo: snapshotRepo}
}

func (s *Service) CreateCamera(ctx context.Context, createCameraDto *dto.CreateCameraDto) (*models.Camera, error) {
	Camera := &models.Camera{
		Name:      createCameraDto.Name,
		Location:  createCameraDto.Location,
		StreamURL: createCameraDto.StreamURL,
		IsActive:  false,
	}
	if createCameraDto.PremiseID != "" {
		premiseID, err := uuid.Parse(createCameraDto.PremiseID)

		if err != nil {
			return nil, err
		}

		premise, err := s.premiseRepo.GetPremiseByID(ctx, premiseID.String())

		if err != nil {
			return nil, err
		}
		Camera.Premise = premise
		Camera.PremiseID = premiseID

	}

	return s.cameraRepo.CreateCamera(ctx, Camera)
}

func (s *Service) GetCameras(ctx context.Context) ([]models.Camera, error) {
	return s.cameraRepo.GetCameras(ctx)
}

func (s *Service) StartPublishing(ctx context.Context, req *dto.SdpRequest) (*dto.SdpResponse, error) {
	broadcaster := webrtc.NewBroadcaster(req.CameraID)
	//mockup sdp
	mockSDP := `v=0
				o=- 46117327 2 IN IP4 127.0.0.1
				s=-
				t=0 0
				a=group:BUNDLE 0
				a=msid-semantic:WMS
				m=audio 9 UDP/TLS/RTP/SAVPF 111
				c=IN IP4 0.0.0.0
				a=rtpmap:111 opus/48000/2
				`

	encoded := base64.StdEncoding.EncodeToString([]byte(mockSDP))

	decoded, err := webrtc.DecodeSDP(encoded)
	if err != nil {
		return nil, err
	}
	answer, err := broadcaster.StartBroadcast(decoded)
	if err != nil {
		return nil, err
	}

	return &dto.SdpResponse{SDP: *answer}, nil
}

// CreateSnapshot handles file upload and creates a snapshot record
func (s *Service) CreateSnapshot(ctx context.Context, req *dto.SnapshotRequest, uploadDir string) (*dto.SnapshotResponse, error) {
	// Validate camera exists
	cameraID, err := uuid.Parse(req.CameraID)
	if err != nil {
		return nil, errors.NewBadRequestError("Invalid camera ID format")
	}

	camera, err := s.cameraRepo.GetCameraByID(ctx, cameraID.String())
	if err != nil {
		return nil, errors.NewNotFoundError("camera")
	}

	// Validate file
	const maxFileSize = 10 * 1024 * 1024 // 10MB
	if err := utils.ValidateImageFile(req.File, maxFileSize); err != nil {
		return nil, errors.NewValidationError("Invalid file", map[string]string{"file": err.Error()})
	}

	// Create upload directory for camera snapshots
	cameraUploadDir := filepath.Join(uploadDir, "snapshots", camera.ID.String())

	// Save the uploaded file
	fileInfo, err := utils.SaveUploadedFile(req.File, cameraUploadDir)
	if err != nil {
		return nil, errors.NewInternalError("Failed to save uploaded file", err)
	}

	// Create snapshot record
	snapshot := &models.Snapshot{
		CameraID: cameraID,
		FileName: fileInfo.SavedName,
		FileSize: fileInfo.Size,
		FileType: fileInfo.ContentType,
		FilePath: fileInfo.Path,
	}

	createdSnapshot, err := s.snapshotRepo.CreateSnapshot(ctx, snapshot)
	if err != nil {
		// Clean up uploaded file if database operation fails
		utils.DeleteFile(fileInfo.Path)
		return nil, errors.NewDatabaseError("create snapshot", err)
	}

	// Return response
	return &dto.SnapshotResponse{
		ID:          createdSnapshot.ID.String(),
		CameraID:    createdSnapshot.CameraID.String(),
		Description: createdSnapshot.Description,
		Timestamp:   createdSnapshot.Timestamp,
		FileName:    createdSnapshot.FileName,
		FileSize:    createdSnapshot.FileSize,
		FileType:    createdSnapshot.FileType,
		FilePath:    createdSnapshot.FilePath,
		CreatedAt:   createdSnapshot.CreatedAt,
	}, nil
}
